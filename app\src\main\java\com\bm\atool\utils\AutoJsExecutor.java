package com.bm.atool.utils;

import android.content.Context;
import android.util.Log;

import com.stardust.autojs.AutoJs;
import com.stardust.autojs.execution.ExecutionConfig;
import com.stardust.autojs.execution.ScriptExecution;
import com.stardust.autojs.execution.ScriptExecutionListener;
import com.stardust.autojs.script.StringScriptSource;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

import io.socket.client.Socket;

/**
 * AutoJs Script Executor
 * Responsible for receiving, executing AutoJs scripts and returning execution results
 */
public class AutoJsExecutor {
    private static final String TAG = "AutoJsExecutor";
    private static AutoJsExecutor instance;
    private AutoJs autoJs;
    private Context context;
    private ConcurrentHashMap<String, ScriptExecution> runningScripts = new ConcurrentHashMap<>();
    private ConcurrentHashMap<String, Long> executionStartTimes = new ConcurrentHashMap<>();
    private ConcurrentHashMap<String, String> executionScriptIds = new ConcurrentHashMap<>();
    private Socket socket;
    
    // 执行统计
    private volatile int totalExecutions = 0;
    private volatile int successfulExecutions = 0;
    private volatile int failedExecutions = 0;

    // Private constructor
    private AutoJsExecutor(Context context) {
        this.context = context.getApplicationContext();
        init();
    }

    // Singleton pattern to get instance
    public static synchronized AutoJsExecutor getInstance(Context context) {
        if (instance == null) {
            instance = new AutoJsExecutor(context);
        }
        return instance;
    }

    // Initialize AutoJs
    private void init() {
        try {
            autoJs = AutoJs.getInstance();
            if (autoJs == null) {
                Log.d(TAG, "Initializing AutoJs");
                autoJs = AutoJs.initInstance(context);
            }
            scriptDownloader = new ScriptDownloader(context);
            Log.i(TAG, "AutoJs initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "AutoJs initialization failed: " + e.getMessage(), e);
        }
    }

    // Set Socket for returning execution results
    public void setSocket(Socket socket) {
        this.socket = socket;
    }

    /**
     * Execute script
     * @param scriptId Script ID
     * @param script Script content
     * @param params Script parameters
     * @return Execution ID
     */
    public String executeScript(String scriptId, String script, Map<String, Object> params) {
        Log.d(TAG, "executeScript called with scriptId: " + scriptId);
        return executeScript(scriptId, script, params, 0); // 默认无超时
    }
    
    /**
     * Execute script with timeout
     * @param scriptId Script ID
     * @param script Script content
     * @param params Script parameters
     * @param timeoutMs Timeout in milliseconds, 0 means no timeout
     * @return Execution ID
     */
    public String executeScript(String scriptId, String script, Map<String, Object> params, long timeoutMs) {
        Log.d(TAG, "executeScript called with scriptId: " + scriptId + ", script length: " + (script != null ? script.length() : 0));
        if (autoJs == null) {
            Log.e(TAG, "AutoJs not initialized");
            sendExecutionResult(scriptId, false, "AutoJs not initialized", null);
            return null;
        }

        // 验证脚本内容
        if (script == null || script.trim().isEmpty()) {
            Log.e(TAG, "Script content is empty for scriptId: " + scriptId);
            sendExecutionResult(scriptId, false, "Script content is empty", null);
            return null;
        }

        String executionId = UUID.randomUUID().toString();
        Log.d(TAG, "Generated executionId: " + executionId);
        
        try {
            // Prepare script configuration
            ExecutionConfig config = new ExecutionConfig();
            config.setDelay(0);
            config.setLoggingEnabled(true);
            config.setStopAllOnEnd(false);
            Log.d(TAG, "Script configuration prepared");

            // Prepare script source
            StringScriptSource scriptSource = new StringScriptSource(script);
            Log.d(TAG, "Script source prepared");
            
            // Set listener
            ScriptExecutionListener listener = new ScriptExecutionListener() {
                @Override
                public void onStart(ScriptExecution execution) {
                    Log.d(TAG, "Script execution started: " + executionId + ", scriptId: " + scriptId);
                    executionStartTimes.put(executionId, System.currentTimeMillis());
                    executionScriptIds.put(executionId, scriptId);
                    totalExecutions++;
                    sendExecutionResult(scriptId, true, "Script execution started", executionId);
                }

                @Override
                public void onSuccess(ScriptExecution execution, Object result) {
                    long duration = calculateExecutionDuration(executionId);
                    Log.d(TAG, "Script execution successful: " + executionId + ", scriptId: " + scriptId + ", duration: " + duration + "ms, result: " + result);
                    
                    cleanupExecution(executionId);
                    successfulExecutions++;
                    
                    Map<String, Object> resultData = new HashMap<>();
                    resultData.put("result", result);
                    resultData.put("duration", duration);
                    sendExecutionResult(scriptId, true, "Script executed successfully", resultData);
                }

                @Override
                public void onException(ScriptExecution execution, Throwable e) {
                    long duration = calculateExecutionDuration(executionId);
                    Log.e(TAG, "Script execution exception: " + executionId + ", scriptId: " + scriptId + ", duration: " + duration + "ms", e);
                    
                    cleanupExecution(executionId);
                    failedExecutions++;
                    
                    Map<String, Object> errorData = new HashMap<>();
                    errorData.put("error", e.getMessage());
                    errorData.put("duration", duration);
                    sendExecutionResult(scriptId, false, "Script execution failed: " + e.getMessage(), errorData);
                }
            };
            Log.d(TAG, "Script execution listener prepared");

            // Execute script
            Log.d(TAG, "About to execute script with executionId: " + executionId);
            ScriptExecution execution = autoJs.getScriptEngineService().execute(
                    scriptSource, config, params, listener);
            Log.d(TAG, "Script execution initiated with executionId: " + executionId);
            
            // Save execution instance
            runningScripts.put(executionId, execution);
            Log.d(TAG, "Script execution instance saved");
            
            // Set timeout if specified
            if (timeoutMs > 0) {
                android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
                handler.postDelayed(() -> {
                    if (runningScripts.containsKey(executionId)) {
                        Log.w(TAG, "Script execution timeout: " + executionId);
                        stopScript(executionId);
                        sendExecutionResult(scriptId, false, "Script execution timeout", null);
                    }
                }, timeoutMs);
            }
            
            Log.d(TAG, "Script execution completed, returning executionId: " + executionId);
            return executionId;
            
        } catch (Exception e) {
            Log.e(TAG, "Execute script exception: " + e.getMessage(), e);
            sendExecutionResult(scriptId, false, "Execute script exception: " + e.getMessage(), null);
            return null;
        }
    }

    /**
     * Stop specified script execution
     * @param executionId Execution ID
     */
    public boolean stopScript(String executionId) {
        ScriptExecution execution = runningScripts.get(executionId);
        if (execution != null) {
            try {
                execution.getEngine().forceStop();
                cleanupExecution(executionId);
                Log.d(TAG, "Script stopped: " + executionId);
                return true;
            } catch (Exception e) {
                Log.e(TAG, "Error stopping script: " + executionId, e);
                cleanupExecution(executionId); // 清理状态即使停止失败
                return false;
            }
        } else {
            Log.w(TAG, "Cannot find running script: " + executionId);
            return false;
        }
    }

    /**
     * Stop all scripts
     */
    public void stopAllScripts() {
        for (Map.Entry<String, ScriptExecution> entry : runningScripts.entrySet()) {
            try {
                entry.getValue().getEngine().forceStop();
                Log.d(TAG, "Script stopped: " + entry.getKey());
            } catch (Exception e) {
                Log.e(TAG, "Stop script exception: " + e.getMessage(), e);
            }
        }
        
        // 清理所有执行状态
        runningScripts.clear();
        executionStartTimes.clear();
        executionScriptIds.clear();
    }

    /**
     * Calculate execution duration
     */
    private long calculateExecutionDuration(String executionId) {
        Long startTime = executionStartTimes.get(executionId);
        if (startTime != null) {
            return System.currentTimeMillis() - startTime;
        }
        return 0;
    }
    
    /**
     * Cleanup execution data
     */
    private void cleanupExecution(String executionId) {
        runningScripts.remove(executionId);
        executionStartTimes.remove(executionId);
        executionScriptIds.remove(executionId);
    }
    
    /**
     * Get execution statistics
     */
    public Map<String, Object> getExecutionStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalExecutions", totalExecutions);
        stats.put("successfulExecutions", successfulExecutions);
        stats.put("failedExecutions", failedExecutions);
        stats.put("runningExecutions", runningScripts.size());
        stats.put("successRate", totalExecutions > 0 ? (double) successfulExecutions / totalExecutions : 0.0);
        return stats;
    }
    
    /**
     * Get running executions info
     */
    public Map<String, Object> getRunningExecutionsInfo() {
        Map<String, Object> info = new HashMap<>();
        for (Map.Entry<String, ScriptExecution> entry : runningScripts.entrySet()) {
            String executionId = entry.getKey();
            String scriptId = executionScriptIds.get(executionId);
            Long startTime = executionStartTimes.get(executionId);
            
            Map<String, Object> executionInfo = new HashMap<>();
            executionInfo.put("scriptId", scriptId);
            executionInfo.put("startTime", startTime);
            executionInfo.put("duration", startTime != null ? System.currentTimeMillis() - startTime : 0);
            
            info.put(executionId, executionInfo);
        }
        return info;
    }
    
    /**
     * Send execution result to Socket
     */
    private void sendExecutionResult(String scriptId, boolean success, String message, Object data) {
        if (socket != null && socket.connected()) {
            Map<String, Object> result = new HashMap<>();
            result.put("scriptId", scriptId);
            result.put("success", success);
            result.put("message", message);
            result.put("data", data);
            result.put("timestamp", System.currentTimeMillis());
            
            socket.emit("scriptResult", new com.google.gson.Gson().toJson(result));
        }
    }
} 